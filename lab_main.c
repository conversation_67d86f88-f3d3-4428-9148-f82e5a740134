#include "board.h"
#include "Solar_F.h"
#include "f28004x_device.h" // Header File Include File
#include "OLED.h"
#include "oledfont.h"
//
#define PI 3.14159265359f
#define TWO_PI (2.0f * PI)
#define PHASE_120_DEG (TWO_PI / 3.0f)
#define PWM_FREQUENCY 20000.0f													// 20kHz�ز�Ƶ��
#define SYSTEM_FREQ 100000000.0f												// 100MHzϵͳʱ��
#define PWM_PERIOD ((Uint16)(SYSTEM_FREQ / (2.0f * PWM_FREQUENCY)))				// 2500
#define ADC_ISR_FREQ 20000.0f													// ADC�ж�Ƶ��20kHz
#define DEAD_TIME_NS 1000.0f													// 死区时间1000ns
#define DEAD_TIME_COUNTS ((Uint16)(DEAD_TIME_NS * SYSTEM_FREQ / 1000000000.0f)) // 死区时间计数值

struct Variable
{
	float V_DCout;
	float I_DCout;
	float Vout_A;
	float Vout_B;
	float Vout_C;
	float Vout_AB;
	float Vout_BC;
	float Ia_ShunShiZhi;
	float Ic_ShunShiZhi;
	float AB_ShunShiZhi;
	float BC_ShunShiZhi;
	float Rms_AB;
	float Rms_BC;
	float Rms_Ia;
	float Rms_Ic;
	float Iout_A;
	float Iout_B;
	float Iout_C;
	float Vref;
	float Iref;
	float duty;
	float PWM_A;
	float PWM_B;
	float PWM_C;
	float average;
	int mode;
	int modulation_type; // 0=SPWM, 1=SVPWM
	float Udc;
	float freq;
	// 滤波器变量
	float PWM_A_filtered;
	float PWM_B_filtered;
	float PWM_C_filtered;

} Variable1;

struct Probe
{
	float probe_a;
	float probe_b;
	float probe_c;
	float probe_d;
	float probe_e;
	float probe_f;
	float probe_g;
} Probe1;

CNTL_2P2Z_F_COEFFS cntl_2p2z_coeffs_PRA;
CNTL_2P2Z_F_VARS cntl_2p2z_vars_PRA;
CNTL_2P2Z_F_COEFFS cntl_2p2z_coeffs_PRB;
CNTL_2P2Z_F_VARS cntl_2p2z_vars_PRB;
CNTL_2P2Z_F_COEFFS cntl_2p2z_coeffs_PRC;
CNTL_2P2Z_F_VARS cntl_2p2z_vars_PRC;
DQ0_ABC_F dq0_abc1;
PID_GRANDO_F_CONTROLLER pid_grando_controller_Vdc;
unsigned long Count = 0;
unsigned long Count1 = 0;
interrupt void INT_myADCA_1_ISR(void);
void SetDuty(float PWM_A, float PWM_B, float PWM_C);
float max_min_average(float a, float b, float c);
void SVPWM_Calculate(float Valpha, float Vbeta, float *Ta, float *Tb, float *Tc);
void ApplyDeadTime(void);
float LowPassFilter(float input, float *prev_output, float alpha);
//
// Main
//
void main(void)
{

	// CPU Initialization
	Device_init();
	Interrupt_initModule();
	Interrupt_initVectorTable();

	DQ0_ABC_F_init(&dq0_abc1);

	// init pr parmarter
	float Ts = 0.00005;
	float wo = 2 * 3.1415926 * 50;
	float Kp = 0.05;
	float Kr = 10;
	float wc = 1;
	float temp = 4 / Ts / Ts + 4 * wc / Ts + wo * wo;

	CNTL_2P2Z_F_COEFFS_init(&cntl_2p2z_coeffs_PRA);
	CNTL_2P2Z_F_COEFFS_init(&cntl_2p2z_coeffs_PRB);
	CNTL_2P2Z_F_COEFFS_init(&cntl_2p2z_coeffs_PRC);

	cntl_2p2z_coeffs_PRA.Coeff_A1 = cntl_2p2z_coeffs_PRB.Coeff_A1 =
		cntl_2p2z_coeffs_PRC.Coeff_A1 = -((-8 / Ts / Ts + 2 * wo * wo) / temp);
	cntl_2p2z_coeffs_PRA.Coeff_A2 = cntl_2p2z_coeffs_PRB.Coeff_A2 =
		cntl_2p2z_coeffs_PRC.Coeff_A2 = -((4 / Ts / Ts - 4 * wc / Ts + wo * wo) / temp);
	cntl_2p2z_coeffs_PRA.Coeff_B0 = cntl_2p2z_coeffs_PRB.Coeff_B0 =
		cntl_2p2z_coeffs_PRC.Coeff_B0 = ((4 * Kp / Ts / Ts + 4 * wc * (Kp + Kr) / Ts + Kp * wo * wo) / temp);
	cntl_2p2z_coeffs_PRA.Coeff_B1 = cntl_2p2z_coeffs_PRB.Coeff_B1 =
		cntl_2p2z_coeffs_PRC.Coeff_B1 = ((-8 * Kp / Ts / Ts + 2 * Kp * wo * wo) / temp);
	cntl_2p2z_coeffs_PRA.Coeff_B2 = cntl_2p2z_coeffs_PRB.Coeff_B2 =
		cntl_2p2z_coeffs_PRC.Coeff_B2 = ((4 * Kp / Ts / Ts - 4 * wc / Ts * (Kp + Kr) + Kp * wo * wo) / temp);
	cntl_2p2z_coeffs_PRA.IMin = cntl_2p2z_coeffs_PRB.IMin =
		cntl_2p2z_coeffs_PRC.IMin = (-10.0);
	cntl_2p2z_coeffs_PRA.Max = cntl_2p2z_coeffs_PRB.Max =
		cntl_2p2z_coeffs_PRC.Max = (10.0);
	cntl_2p2z_coeffs_PRA.Min = cntl_2p2z_coeffs_PRB.Min =
		cntl_2p2z_coeffs_PRC.Min = (-10.0);
	CNTL_2P2Z_F_VARS_init(&cntl_2p2z_vars_PRA);
	CNTL_2P2Z_F_VARS_init(&cntl_2p2z_vars_PRB);
	CNTL_2P2Z_F_VARS_init(&cntl_2p2z_vars_PRC);

	// init pi parmarter
	PID_GRANDO_F_init(&pid_grando_controller_Vdc);
	pid_grando_controller_Vdc.param.Kp = (0.00005);
	pid_grando_controller_Vdc.param.Ki = (0.03);
	pid_grando_controller_Vdc.param.Kd = (0.0);
	pid_grando_controller_Vdc.param.Kr = (1.0);
	pid_grando_controller_Vdc.param.Umax = (100);
	pid_grando_controller_Vdc.param.Umin = (0);

	// Enable global interrupts and real-time debug
	EINT;
	ERTM;

	// ��ʼ������
	Probe1.probe_a = 0.991699994;
	Probe1.probe_b = 124.1;
	Probe1.probe_c = 124.1;
	Probe1.probe_d = 1;
	Variable1.Vref = 32;
	Variable1.Iref = 2;
	Variable1.duty = 0.9;
	Variable1.mode = 1;
	Variable1.modulation_type = 0; // 默认使用SPWM
	Variable1.freq = 50.0f;
	// 初始化滤波器变量
	Variable1.PWM_A_filtered = 0.5f;
	Variable1.PWM_B_filtered = 0.5f;
	Variable1.PWM_C_filtered = 0.5f;

	SysCtl_disablePeripheral(SYSCTL_PERIPH_CLK_TBCLKSYNC);
	Board_init();
	SysCtl_enablePeripheral(SYSCTL_PERIPH_CLK_TBCLKSYNC);

	// 配置死区时间
	ApplyDeadTime();

	OLED_Init();
	OLED_Clear();

	// Main Loop
	while (1)
	{
		OLED_ShowNum(0, 0, Variable1.Vref, 3, 2, 16);
		OLED_ShowNum(0, 2, Variable1.Rms_BC, 3, 2, 16);
		OLED_ShowNum(0, 4, Variable1.freq, 3, 2, 16);
		OLED_ShowNum(0, 6, Variable1.mode, 3, 2, 16);
		OLED_ShowNum(64, 6, Variable1.modulation_type, 1, 2, 16); // 显示调制类型

		// ����ģʽ�µ�Ƶ�ʵ���
		if (Variable1.mode == 1)
		{
			if (GpioDataRegs.GPADAT.bit.GPIO16 == 0)
			{
				while (GpioDataRegs.GPADAT.bit.GPIO16 == 0)
					;
				Variable1.freq = Variable1.freq + 1.0f;
				if (Variable1.freq > 100.0f)
					Variable1.freq = 100.0f;
			}
			if (GpioDataRegs.GPADAT.bit.GPIO17 == 0)
			{
				while (GpioDataRegs.GPADAT.bit.GPIO17 == 0)
					;
				Variable1.freq = Variable1.freq - 1.0f;
				if (Variable1.freq < 20.0f)
					Variable1.freq = 20.0f;
			}
		}

		// ��ѹ����ģʽ
		if (Variable1.mode == 2)
		{
			if (GpioDataRegs.GPADAT.bit.GPIO16 == 0)
			{
				while (GpioDataRegs.GPADAT.bit.GPIO16 == 0)
					;
				Variable1.Vref = Variable1.Vref + 0.5;
			}
			if (GpioDataRegs.GPADAT.bit.GPIO17 == 0)
			{
				while (GpioDataRegs.GPADAT.bit.GPIO17 == 0)
					;
				Variable1.Vref = Variable1.Vref - 0.5;
			}
		}

		// ģʽ�л�����
		if (GpioDataRegs.GPBDAT.bit.GPIO56 == 0)
		{
			while (GpioDataRegs.GPBDAT.bit.GPIO56 == 0)
				;
			Variable1.mode = 1;
		}

		if (GpioDataRegs.GPBDAT.bit.GPIO57 == 0)
		{
			while (GpioDataRegs.GPBDAT.bit.GPIO57 == 0)
				;
			Variable1.mode = 2;
		}

		// 调制模式切换 - 同时按下GPIO16和GPIO17切换SPWM/SVPWM
		if (GpioDataRegs.GPADAT.bit.GPIO16 == 0 && GpioDataRegs.GPADAT.bit.GPIO17 == 0)
		{
			while (GpioDataRegs.GPADAT.bit.GPIO16 == 0 || GpioDataRegs.GPADAT.bit.GPIO17 == 0)
				;
			Variable1.modulation_type = 1 - Variable1.modulation_type; // 在0和1之间切换
		}
	}
}

interrupt void INT_myADCA_1_ISR(void)
{
	// ADC��ȡ����
	Variable1.Iout_A = ((float)AdcbResultRegs.ADCRESULT1 - (float)AdcbResultRegs.ADCRESULT0 + 1) * 0.004;
	Variable1.Iout_C = ((float)AdcbResultRegs.ADCRESULT2 + 7 - (float)AdcbResultRegs.ADCRESULT3) * 0.004;

	Variable1.Vout_AB = ((float)AdccResultRegs.ADCRESULT2 - (float)AdccResultRegs.ADCRESULT3 + 2.1) * 0.032601;
	Variable1.Vout_BC = ((float)AdccResultRegs.ADCRESULT0 - (float)AdccResultRegs.ADCRESULT1 - 3.33) * 0.0275815;
	// Variable1.Vout_AB =((float)AdccResultRegs.ADCRESULT0);
	// Variable1.Vout_BC =((float)AdccResultRegs.ADCRESULT1);

	Variable1.Vout_A = (Variable1.Vout_AB + 2 * Variable1.Vout_BC);
	Variable1.Vout_C = -(Variable1.Vout_BC + 2 * Variable1.Vout_AB);

	Variable1.Ia_ShunShiZhi = Variable1.Iout_A * Variable1.Iout_A + Variable1.Ia_ShunShiZhi;
	Variable1.Ic_ShunShiZhi = Variable1.Iout_C * Variable1.Iout_C + Variable1.Ic_ShunShiZhi;
	Variable1.AB_ShunShiZhi = Variable1.Vout_AB * Variable1.Vout_AB + Variable1.AB_ShunShiZhi;
	Variable1.BC_ShunShiZhi = Variable1.Vout_BC * Variable1.Vout_BC + Variable1.BC_ShunShiZhi;

	if (++Count1 >= 400)
	{
		Variable1.Rms_AB = __sqrt(__divf32(Variable1.AB_ShunShiZhi, 400));
		Variable1.Rms_BC = __sqrt(__divf32(Variable1.BC_ShunShiZhi, 400));
		Variable1.Rms_Ia = __sqrt(__divf32(Variable1.Ia_ShunShiZhi, 400));
		Variable1.Rms_Ic = __sqrt(__divf32(Variable1.Ic_ShunShiZhi, 400));
		Variable1.AB_ShunShiZhi = 0;
		Variable1.BC_ShunShiZhi = 0;
		Variable1.Ia_ShunShiZhi = 0;
		Variable1.Ic_ShunShiZhi = 0;
		Count1 = 0;
	}

	if (++Count >= 40000000)
	{
		Count = 0;
	}
	// �������� - ʹ�ÿɱ�Ƶ��
	if (Variable1.mode == 1)
	{
		if (Variable1.modulation_type == 0) // SPWM调制
		{
			dq0_abc1.q = Probe1.probe_a;
			dq0_abc1.sin = __sinpuf32(Variable1.freq * Count * 0.00005);
			dq0_abc1.cos = __cospuf32(Variable1.freq * Count * 0.00005);
			DQ0_ABC_F_MACRO(dq0_abc1);
			Variable1.average = max_min_average(dq0_abc1.a, dq0_abc1.b, dq0_abc1.c);
			EPwm1Regs.CMPA.bit.CMPA = (dq0_abc1.a - Variable1.average + 1) * 1250;
			EPwm2Regs.CMPA.bit.CMPA = (dq0_abc1.b - Variable1.average + 1) * 1250;
			EPwm3Regs.CMPA.bit.CMPA = (dq0_abc1.c - Variable1.average + 1) * 1250;
		}
		else // SVPWM调制
		{
			float Valpha, Vbeta;
			float angle = Variable1.freq * Count * 0.00005 * TWO_PI;

			// 生成alpha-beta参考电压
			Valpha = Probe1.probe_a * __cospuf32(angle);
			Vbeta = Probe1.probe_a * __sinpuf32(angle);

			// 计算SVPWM占空比
			SVPWM_Calculate(Valpha, Vbeta, &Variable1.PWM_A, &Variable1.PWM_B, &Variable1.PWM_C);

			// 应用低通滤波器改善波形质量
			Variable1.PWM_A_filtered = LowPassFilter(Variable1.PWM_A, &Variable1.PWM_A_filtered, 0.1f);
			Variable1.PWM_B_filtered = LowPassFilter(Variable1.PWM_B, &Variable1.PWM_B_filtered, 0.1f);
			Variable1.PWM_C_filtered = LowPassFilter(Variable1.PWM_C, &Variable1.PWM_C_filtered, 0.1f);

			// 应用PWM占空比
			EPwm1Regs.CMPA.bit.CMPA = (Uint16)(Variable1.PWM_C_filtered * 2500);
			EPwm2Regs.CMPA.bit.CMPA = (Uint16)(Variable1.PWM_B_filtered * 2500);
			EPwm3Regs.CMPA.bit.CMPA = (Uint16)(Variable1.PWM_A_filtered * 2500);
		}
	}

	if (Variable1.mode == 2)
	{
		pid_grando_controller_Vdc.term.Ref = Variable1.Vref;
		pid_grando_controller_Vdc.term.Fbk = Variable1.Rms_BC;
		PID_GRANDO_F_MACRO(pid_grando_controller_Vdc);
		// into pr A phase
		cntl_2p2z_vars_PRA.Ref = pid_grando_controller_Vdc.term.Out * __sinpuf32(50 * Count * 0.00005);
		cntl_2p2z_vars_PRA.Fdbk = Variable1.Iout_A;
		CNTL_2P2Z_F_MACRO(cntl_2p2z_coeffs_PRA, cntl_2p2z_vars_PRA);
		// into pr C phase
		cntl_2p2z_vars_PRC.Ref = pid_grando_controller_Vdc.term.Out * __sinpuf32(50 * Count * 0.00005 + 0.66666666);
		cntl_2p2z_vars_PRC.Fdbk = Variable1.Iout_C;
		CNTL_2P2Z_F_MACRO(cntl_2p2z_coeffs_PRC, cntl_2p2z_vars_PRC);
		// Variable1.average = max_min_average(dq0_abc1.a, dq0_abc1.b, dq0_abc1.c);
		Variable1.PWM_A = cntl_2p2z_vars_PRA.Out;
		Variable1.PWM_C = cntl_2p2z_vars_PRC.Out;
		Variable1.PWM_B = -Variable1.PWM_A - Variable1.PWM_C;
		Variable1.average = max_min_average(Variable1.PWM_A, Variable1.PWM_B, Variable1.PWM_C);
		EPwm3Regs.CMPA.bit.CMPA = (Variable1.PWM_A - Variable1.average + 1) * 1250;
		EPwm2Regs.CMPA.bit.CMPA = (Variable1.PWM_B - Variable1.average + 1) * 1250;
		EPwm1Regs.CMPA.bit.CMPA = (Variable1.PWM_C - Variable1.average + 1) * 1250;
	}
	// set DAC output
	switch ((int)Probe1.probe_d)
	{
	case 0:
		DacaRegs.DACVALS.all = (uint16_t)(dq0_abc1.a * Probe1.probe_b + 2048);
		DacbRegs.DACVALS.all = (uint16_t)(dq0_abc1.b * Probe1.probe_c + 2048);
		break;
	case 1:
		DacaRegs.DACVALS.all = (uint16_t)(Variable1.Iout_A * Probe1.probe_b + 2048);
		DacbRegs.DACVALS.all = (uint16_t)(Variable1.Iout_C * Probe1.probe_c + 2048);
		break;
	case 2:
		DacaRegs.DACVALS.all = EPwm3Regs.CMPA.bit.CMPA;
		DacbRegs.DACVALS.all = EPwm2Regs.CMPA.bit.CMPA;
		break;
	}
	Interrupt_clearACKGroup(INT_myADCA_1_INTERRUPT_ACK_GROUP);
	ADC_clearInterruptStatus(myADCA_BASE, ADC_INT_NUMBER1);
}
interrupt void INT_myEPWM1_TZ_ISR(void)
{
	while (1)
	{
	}
}
float max_min_average(float a, float b, float c)
{
	float max = a;
	if (b > max)
		max = b;
	if (c > max)
		max = c;

	float min = a;
	if (b < min)
		min = b;
	if (c < min)
		min = c;

	return (max + min) / 2.0f;
}
void SetDuty(float PWM_A, float PWM_B, float PWM_C)
{
	EPwm1Regs.CMPA.bit.CMPA = (Uint16)(1250 * (PWM_A + 1));
	EPwm2Regs.CMPA.bit.CMPA = (Uint16)(1250 * (PWM_B + 1));
	EPwm3Regs.CMPA.bit.CMPA = (Uint16)(1250 * (PWM_C + 1));
}

// SVPWM计算函数 - 改善波形质量
void SVPWM_Calculate(float Valpha, float Vbeta, float *Ta, float *Tb, float *Tc)
{
	float Uref, angle, T1, T2, T0;
	float sqrt3 = 1.732050808f;
	int sector;

	// 计算参考电压幅值和角度
	Uref = __sqrt(Valpha * Valpha + Vbeta * Vbeta);
	angle = __atan2puf32(Vbeta, Valpha);

	// 限制调制度
	if (Uref > 0.866f)
		Uref = 0.866f; // 限制在线性调制区域

	// 确定扇区
	if (angle < 0)
		angle += TWO_PI;
	sector = (int)(angle / (PI / 3.0f));

	// 计算扇区内角度
	float theta = angle - sector * (PI / 3.0f);

	// 计算基本矢量作用时间
	T1 = sqrt3 * Uref * __sinpuf32((PI / 3.0f) - theta);
	T2 = sqrt3 * Uref * __sinpuf32(theta);
	T0 = 1.0f - T1 - T2;

	// 根据扇区计算三相占空比
	switch (sector)
	{
	case 0: // 扇区1
		*Ta = T0 / 2.0f + T1 + T2;
		*Tb = T0 / 2.0f + T2;
		*Tc = T0 / 2.0f;
		break;
	case 1: // 扇区2
		*Ta = T0 / 2.0f + T1;
		*Tb = T0 / 2.0f + T1 + T2;
		*Tc = T0 / 2.0f;
		break;
	case 2: // 扇区3
		*Ta = T0 / 2.0f;
		*Tb = T0 / 2.0f + T1 + T2;
		*Tc = T0 / 2.0f + T2;
		break;
	case 3: // 扇区4
		*Ta = T0 / 2.0f;
		*Tb = T0 / 2.0f + T1;
		*Tc = T0 / 2.0f + T1 + T2;
		break;
	case 4: // 扇区5
		*Ta = T0 / 2.0f + T2;
		*Tb = T0 / 2.0f;
		*Tc = T0 / 2.0f + T1 + T2;
		break;
	case 5: // 扇区6
		*Ta = T0 / 2.0f + T1 + T2;
		*Tb = T0 / 2.0f;
		*Tc = T0 / 2.0f + T1;
		break;
	default:
		*Ta = *Tb = *Tc = 0.5f;
		break;
	}
}

// 应用死区时间配置
void ApplyDeadTime(void)
{
	// 配置EPWM1死区
	EPWM_setDeadBandDelayPolarity(EPWM1_BASE, EPWM_DB_RED, EPWM_DB_POLARITY_ACTIVE_HIGH);
	EPWM_setDeadBandDelayPolarity(EPWM1_BASE, EPWM_DB_FED, EPWM_DB_POLARITY_ACTIVE_LOW);
	EPWM_setDeadBandDelayMode(EPWM1_BASE, EPWM_DB_RED, true);
	EPWM_setDeadBandDelayMode(EPWM1_BASE, EPWM_DB_FED, true);
	EPWM_setRisingEdgeDeadBandDelayInput(EPWM1_BASE, EPWM_DB_INPUT_EPWMA);
	EPWM_setFallingEdgeDeadBandDelayInput(EPWM1_BASE, EPWM_DB_INPUT_EPWMA);
	EPWM_setDeadBandCounterClock(EPWM1_BASE, EPWM_DB_COUNTER_CLOCK_FULL_CYCLE);
	EPWM_setRisingEdgeDelayCount(EPWM1_BASE, DEAD_TIME_COUNTS);
	EPWM_setFallingEdgeDelayCount(EPWM1_BASE, DEAD_TIME_COUNTS);

	// 配置EPWM2死区
	EPWM_setDeadBandDelayPolarity(EPWM2_BASE, EPWM_DB_RED, EPWM_DB_POLARITY_ACTIVE_HIGH);
	EPWM_setDeadBandDelayPolarity(EPWM2_BASE, EPWM_DB_FED, EPWM_DB_POLARITY_ACTIVE_LOW);
	EPWM_setDeadBandDelayMode(EPWM2_BASE, EPWM_DB_RED, true);
	EPWM_setDeadBandDelayMode(EPWM2_BASE, EPWM_DB_FED, true);
	EPWM_setRisingEdgeDeadBandDelayInput(EPWM2_BASE, EPWM_DB_INPUT_EPWMA);
	EPWM_setFallingEdgeDeadBandDelayInput(EPWM2_BASE, EPWM_DB_INPUT_EPWMA);
	EPWM_setDeadBandCounterClock(EPWM2_BASE, EPWM_DB_COUNTER_CLOCK_FULL_CYCLE);
	EPWM_setRisingEdgeDelayCount(EPWM2_BASE, DEAD_TIME_COUNTS);
	EPWM_setFallingEdgeDelayCount(EPWM2_BASE, DEAD_TIME_COUNTS);

	// 配置EPWM3死区
	EPWM_setDeadBandDelayPolarity(EPWM3_BASE, EPWM_DB_RED, EPWM_DB_POLARITY_ACTIVE_HIGH);
	EPWM_setDeadBandDelayPolarity(EPWM3_BASE, EPWM_DB_FED, EPWM_DB_POLARITY_ACTIVE_LOW);
	EPWM_setDeadBandDelayMode(EPWM3_BASE, EPWM_DB_RED, true);
	EPWM_setDeadBandDelayMode(EPWM3_BASE, EPWM_DB_FED, true);
	EPWM_setRisingEdgeDeadBandDelayInput(EPWM3_BASE, EPWM_DB_INPUT_EPWMA);
	EPWM_setFallingEdgeDeadBandDelayInput(EPWM3_BASE, EPWM_DB_INPUT_EPWMA);
	EPWM_setDeadBandCounterClock(EPWM3_BASE, EPWM_DB_COUNTER_CLOCK_FULL_CYCLE);
	EPWM_setRisingEdgeDelayCount(EPWM3_BASE, DEAD_TIME_COUNTS);
	EPWM_setFallingEdgeDelayCount(EPWM3_BASE, DEAD_TIME_COUNTS);
}

// 低通滤波器函数 - 改善PWM波形
float LowPassFilter(float input, float *prev_output, float alpha)
{
	*prev_output = alpha * input + (1.0f - alpha) * (*prev_output);
	return *prev_output;
}

// End of ADC ISR
//
//

//
