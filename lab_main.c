#include "board.h"
#include "Solar_F.h"
#include "f28004x_device.h" // Header File Include File
#include "OLED.h"
#include "oledfont.h"
//
#define PI 3.14159265359f
#define TWO_PI (2.0f * PI)
#define PHASE_120_DEG (TWO_PI / 3.0f)
#define PWM_FREQUENCY 20000.0f  // 20kHz�ز�Ƶ��
#define SYSTEM_FREQ 100000000.0f  // 100MHzϵͳʱ��
#define PWM_PERIOD ((Uint16)(SYSTEM_FREQ / (2.0f * PWM_FREQUENCY)))  // 2500
#define ADC_ISR_FREQ 20000.0f  // ADC�ж�Ƶ��20kHz

struct Variable
{
	float V_DCout;
	float I_DCout;
	float Vout_A;
	float Vout_B;
	float Vout_C;
	float Vout_AB;
	float Vout_BC;
	float Ia_ShunShiZhi;
	float Ic_ShunShiZhi;
	float AB_ShunShiZhi;
	float BC_ShunShiZhi;
	float Rms_AB;
	float Rms_BC;
	float Rms_Ia;
	float Rms_Ic;
	float Iout_A;
	float Iout_B;
	float Iout_C;
	float Vref;
	float Iref;
	float duty;
	float PWM_A;
	float PWM_B;
	float PWM_C;
	float average;
	int mode;
	float Udc;
	float freq;

} Variable1;

struct Probe
{
	float probe_a;
	float probe_b;
	float probe_c;
	float probe_d;
	float probe_e;
	float probe_f;
	float probe_g;
} Probe1;

CNTL_2P2Z_F_COEFFS cntl_2p2z_coeffs_PRA;
CNTL_2P2Z_F_VARS cntl_2p2z_vars_PRA;
CNTL_2P2Z_F_COEFFS cntl_2p2z_coeffs_PRB;
CNTL_2P2Z_F_VARS cntl_2p2z_vars_PRB;
CNTL_2P2Z_F_COEFFS cntl_2p2z_coeffs_PRC;
CNTL_2P2Z_F_VARS cntl_2p2z_vars_PRC;
DQ0_ABC_F dq0_abc1;
PID_GRANDO_F_CONTROLLER pid_grando_controller_Vdc;
unsigned long Count = 0;
unsigned long Count1 = 0;
interrupt void INT_myADCA_1_ISR(void);
void SetDuty(float PWM_A, float PWM_B, float PWM_C);
float max_min_average(float a, float b, float c);
//
// Main
//
void main(void)
{

	// CPU Initialization
	Device_init();
	Interrupt_initModule();
	Interrupt_initVectorTable();

	DQ0_ABC_F_init(&dq0_abc1);

	// init pr parmarter
	float Ts = 0.00005;
	float wo = 2 * 3.1415926 * 50;
	float Kp = 0.05;
	float Kr = 10;
	float wc = 1;
	float temp = 4 / Ts / Ts + 4 * wc / Ts + wo * wo;

	CNTL_2P2Z_F_COEFFS_init(&cntl_2p2z_coeffs_PRA);
	CNTL_2P2Z_F_COEFFS_init(&cntl_2p2z_coeffs_PRB);
	CNTL_2P2Z_F_COEFFS_init(&cntl_2p2z_coeffs_PRC);

	cntl_2p2z_coeffs_PRA.Coeff_A1 = cntl_2p2z_coeffs_PRB.Coeff_A1 =
		cntl_2p2z_coeffs_PRC.Coeff_A1 = -((-8 / Ts / Ts + 2 * wo * wo) / temp);
	cntl_2p2z_coeffs_PRA.Coeff_A2 = cntl_2p2z_coeffs_PRB.Coeff_A2 =
		cntl_2p2z_coeffs_PRC.Coeff_A2 = -((4 / Ts / Ts - 4 * wc / Ts + wo * wo) / temp);
	cntl_2p2z_coeffs_PRA.Coeff_B0 = cntl_2p2z_coeffs_PRB.Coeff_B0 =
		cntl_2p2z_coeffs_PRC.Coeff_B0 = ((4 * Kp / Ts / Ts + 4 * wc * (Kp + Kr) / Ts + Kp * wo * wo) / temp);
	cntl_2p2z_coeffs_PRA.Coeff_B1 = cntl_2p2z_coeffs_PRB.Coeff_B1 =
		cntl_2p2z_coeffs_PRC.Coeff_B1 = ((-8 * Kp / Ts / Ts + 2 * Kp * wo * wo) / temp);
	cntl_2p2z_coeffs_PRA.Coeff_B2 = cntl_2p2z_coeffs_PRB.Coeff_B2 =
		cntl_2p2z_coeffs_PRC.Coeff_B2 = ((4 * Kp / Ts / Ts - 4 * wc / Ts * (Kp + Kr) + Kp * wo * wo) / temp);
	cntl_2p2z_coeffs_PRA.IMin = cntl_2p2z_coeffs_PRB.IMin =
		cntl_2p2z_coeffs_PRC.IMin = (-10.0);
	cntl_2p2z_coeffs_PRA.Max = cntl_2p2z_coeffs_PRB.Max =
		cntl_2p2z_coeffs_PRC.Max = (10.0);
	cntl_2p2z_coeffs_PRA.Min = cntl_2p2z_coeffs_PRB.Min =
		cntl_2p2z_coeffs_PRC.Min = (-10.0);
	CNTL_2P2Z_F_VARS_init(&cntl_2p2z_vars_PRA);
	CNTL_2P2Z_F_VARS_init(&cntl_2p2z_vars_PRB);
	CNTL_2P2Z_F_VARS_init(&cntl_2p2z_vars_PRC);

	// init pi parmarter
	PID_GRANDO_F_init(&pid_grando_controller_Vdc);
	pid_grando_controller_Vdc.param.Kp = (0.00005);
	pid_grando_controller_Vdc.param.Ki = (0.03);
	pid_grando_controller_Vdc.param.Kd = (0.0);
	pid_grando_controller_Vdc.param.Kr = (1.0);
	pid_grando_controller_Vdc.param.Umax = (100);
	pid_grando_controller_Vdc.param.Umin = (0);

	// Enable global interrupts and real-time debug
	EINT;
	ERTM;

	// ��ʼ������
	Probe1.probe_a = 0.991699994;
	Probe1.probe_b = 124.1;
	Probe1.probe_c = 124.1;
	Probe1.probe_d = 1;
	Variable1.Vref = 32;
	Variable1.Iref = 2;
	Variable1.duty = 0.9;
	Variable1.mode = 1;
	Variable1.freq = 50.0f;

	SysCtl_disablePeripheral(SYSCTL_PERIPH_CLK_TBCLKSYNC);
	Board_init();
	SysCtl_enablePeripheral(SYSCTL_PERIPH_CLK_TBCLKSYNC);
	OLED_Init();
	OLED_Clear();

	// Main Loop
	while (1)
	{
		OLED_ShowNum(0, 0, Variable1.Vref, 3, 2, 16);
		OLED_ShowNum(0, 2, Variable1.Rms_BC, 3, 2, 16);
		OLED_ShowNum(0, 4, Variable1.freq, 3, 2, 16);
		OLED_ShowNum(0, 6, Variable1.mode, 3, 2, 16);

		// ����ģʽ�µ�Ƶ�ʵ���
		if (Variable1.mode == 1)
		{
			if (GpioDataRegs.GPADAT.bit.GPIO16 == 0)
			{
				while (GpioDataRegs.GPADAT.bit.GPIO16 == 0)
					;
				Variable1.freq = Variable1.freq + 1.0f;
				if (Variable1.freq > 100.0f)
					Variable1.freq = 100.0f;
			}
			if (GpioDataRegs.GPADAT.bit.GPIO17 == 0)
			{
				while (GpioDataRegs.GPADAT.bit.GPIO17 == 0)
					;
				Variable1.freq = Variable1.freq - 1.0f;
				if (Variable1.freq < 20.0f)
					Variable1.freq = 20.0f;
			}
		}

		// ��ѹ����ģʽ
		if (Variable1.mode == 2)
		{
			if (GpioDataRegs.GPADAT.bit.GPIO16 == 0)
			{
				while (GpioDataRegs.GPADAT.bit.GPIO16 == 0)
					;
				Variable1.Vref = Variable1.Vref + 0.5;
			}
			if (GpioDataRegs.GPADAT.bit.GPIO17 == 0)
			{
				while (GpioDataRegs.GPADAT.bit.GPIO17 == 0)
					;
				Variable1.Vref = Variable1.Vref - 0.5;
			}
		}

		// ģʽ�л�����
		if (GpioDataRegs.GPBDAT.bit.GPIO56 == 0)
		{
			while (GpioDataRegs.GPBDAT.bit.GPIO56 == 0)
				;
			Variable1.mode = 1;
		}

		if (GpioDataRegs.GPBDAT.bit.GPIO57 == 0)
		{
			while (GpioDataRegs.GPBDAT.bit.GPIO57 == 0)
				;
			Variable1.mode = 2;
		}
	}
}

interrupt void INT_myADCA_1_ISR(void)
{
	// ADC��ȡ����
	Variable1.Iout_A = ((float)AdcbResultRegs.ADCRESULT1 - (float)AdcbResultRegs.ADCRESULT0 + 1) * 0.004;
	Variable1.Iout_C = ((float)AdcbResultRegs.ADCRESULT2 + 7 - (float)AdcbResultRegs.ADCRESULT3) * 0.004;

	Variable1.Vout_AB = ((float)AdccResultRegs.ADCRESULT2 - (float)AdccResultRegs.ADCRESULT3 + 2.1) * 0.032601;
	Variable1.Vout_BC = ((float)AdccResultRegs.ADCRESULT0 - (float)AdccResultRegs.ADCRESULT1 - 3.33) * 0.0275815;
	// Variable1.Vout_AB =((float)AdccResultRegs.ADCRESULT0);
	// Variable1.Vout_BC =((float)AdccResultRegs.ADCRESULT1);

	Variable1.Vout_A = (Variable1.Vout_AB + 2 * Variable1.Vout_BC);
	Variable1.Vout_C = -(Variable1.Vout_BC + 2 * Variable1.Vout_AB);

	Variable1.Ia_ShunShiZhi = Variable1.Iout_A * Variable1.Iout_A + Variable1.Ia_ShunShiZhi;
	Variable1.Ic_ShunShiZhi = Variable1.Iout_C * Variable1.Iout_C + Variable1.Ic_ShunShiZhi;
	Variable1.AB_ShunShiZhi = Variable1.Vout_AB * Variable1.Vout_AB + Variable1.AB_ShunShiZhi;
	Variable1.BC_ShunShiZhi = Variable1.Vout_BC * Variable1.Vout_BC + Variable1.BC_ShunShiZhi;

	if (++Count1 >= 400)
	{
		Variable1.Rms_AB = __sqrt(__divf32(Variable1.AB_ShunShiZhi, 400));
		Variable1.Rms_BC = __sqrt(__divf32(Variable1.BC_ShunShiZhi, 400));
		Variable1.Rms_Ia = __sqrt(__divf32(Variable1.Ia_ShunShiZhi, 400));
		Variable1.Rms_Ic = __sqrt(__divf32(Variable1.Ic_ShunShiZhi, 400));
		Variable1.AB_ShunShiZhi = 0;
		Variable1.BC_ShunShiZhi = 0;
		Variable1.Ia_ShunShiZhi = 0;
		Variable1.Ic_ShunShiZhi = 0;
		Count1 = 0;
	}

	if (++Count >= 40000000)
	{
		Count = 0;
	}
	// �������� - ʹ�ÿɱ�Ƶ��
	if (Variable1.mode == 1)
	{
		dq0_abc1.q = Probe1.probe_a;
		dq0_abc1.sin = __sinpuf32(Variable1.freq * Count * 0.00005);
		dq0_abc1.cos = __cospuf32(Variable1.freq * Count * 0.00005);
		DQ0_ABC_F_MACRO(dq0_abc1);
		Variable1.average = max_min_average(dq0_abc1.a, dq0_abc1.b, dq0_abc1.c);
		EPwm1Regs.CMPA.bit.CMPA = (dq0_abc1.a - Variable1.average + 1) * 1250;
		EPwm2Regs.CMPA.bit.CMPA = (dq0_abc1.b - Variable1.average + 1) * 1250;
		EPwm3Regs.CMPA.bit.CMPA = (dq0_abc1.c - Variable1.average + 1) * 1250;
	}

	if (Variable1.mode == 2)
	{
		pid_grando_controller_Vdc.term.Ref = Variable1.Vref;
		pid_grando_controller_Vdc.term.Fbk = Variable1.Rms_BC;
		PID_GRANDO_F_MACRO(pid_grando_controller_Vdc);
		// into pr A phase
		cntl_2p2z_vars_PRA.Ref = pid_grando_controller_Vdc.term.Out * __sinpuf32(50 * Count * 0.00005);
		cntl_2p2z_vars_PRA.Fdbk = Variable1.Iout_A;
		CNTL_2P2Z_F_MACRO(cntl_2p2z_coeffs_PRA, cntl_2p2z_vars_PRA);
		// into pr C phase
		cntl_2p2z_vars_PRC.Ref = pid_grando_controller_Vdc.term.Out * __sinpuf32(50 * Count * 0.00005 + 0.66666666);
		cntl_2p2z_vars_PRC.Fdbk = Variable1.Iout_C;
		CNTL_2P2Z_F_MACRO(cntl_2p2z_coeffs_PRC, cntl_2p2z_vars_PRC);
		// Variable1.average = max_min_average(dq0_abc1.a, dq0_abc1.b, dq0_abc1.c);
		Variable1.PWM_A = cntl_2p2z_vars_PRA.Out;
		Variable1.PWM_C = cntl_2p2z_vars_PRC.Out;
		Variable1.PWM_B = -Variable1.PWM_A - Variable1.PWM_C;
		Variable1.average = max_min_average(Variable1.PWM_A, Variable1.PWM_B, Variable1.PWM_C);
		EPwm3Regs.CMPA.bit.CMPA = (Variable1.PWM_A - Variable1.average + 1) * 1250;
		EPwm2Regs.CMPA.bit.CMPA = (Variable1.PWM_B - Variable1.average + 1) * 1250;
		EPwm1Regs.CMPA.bit.CMPA = (Variable1.PWM_C - Variable1.average + 1) * 1250;
	}
	// set DAC output
	switch ((int)Probe1.probe_d)
	{
	case 0:
		DacaRegs.DACVALS.all = (uint16_t)(dq0_abc1.a * Probe1.probe_b + 2048);
		DacbRegs.DACVALS.all = (uint16_t)(dq0_abc1.b * Probe1.probe_c + 2048);
		break;
	case 1:
		DacaRegs.DACVALS.all = (uint16_t)(Variable1.Iout_A * Probe1.probe_b + 2048);
		DacbRegs.DACVALS.all = (uint16_t)(Variable1.Iout_C * Probe1.probe_c + 2048);
		break;
	case 2:
		DacaRegs.DACVALS.all = EPwm3Regs.CMPA.bit.CMPA;
		DacbRegs.DACVALS.all = EPwm2Regs.CMPA.bit.CMPA;
		break;
	}
	Interrupt_clearACKGroup(INT_myADCA_1_INTERRUPT_ACK_GROUP);
	ADC_clearInterruptStatus(myADCA_BASE, ADC_INT_NUMBER1);
}
interrupt void INT_myEPWM1_TZ_ISR(void)
{
	while (1)
	{
	}
}
float max_min_average(float a, float b, float c)
{
	float max = a;
	if (b > max)
		max = b;
	if (c > max)
		max = c;

	float min = a;
	if (b < min)
		min = b;
	if (c < min)
		min = c;

	return (max + min) / 2.0f;
}
void SetDuty(float PWM_A, float PWM_B, float PWM_C)
{
	EPwm1Regs.CMPA.bit.CMPA = (Uint16)(1250 * (PWM_A + 1));
	EPwm2Regs.CMPA.bit.CMPA = (Uint16)(1250 * (PWM_B + 1));
	EPwm3Regs.CMPA.bit.CMPA = (Uint16)(1250 * (PWM_C + 1));
}
// End of ADC ISR
//
//

//
